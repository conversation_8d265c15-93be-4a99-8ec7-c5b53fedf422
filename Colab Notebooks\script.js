// DOM elements
const uploadArea = document.getElementById('uploadArea');
const imageInput = document.getElementById('imageInput');
const imagePreview = document.getElementById('imagePreview');
const resultsSection = document.getElementById('resultsSection');
const analyzeBtn = document.getElementById('analyzeBtn');
const newAnalysisBtn = document.getElementById('newAnalysisBtn');
const loadingSection = document.getElementById('loadingSection');
const resultText = document.getElementById('resultText');
const confidenceFill = document.getElementById('confidenceFill');
const confidenceText = document.getElementById('confidenceText');

// Event listeners
uploadArea.addEventListener('click', () => imageInput.click());
uploadArea.addEventListener('dragover', handleDragOver);
uploadArea.addEventListener('dragleave', handleDragLeave);
uploadArea.addEventListener('drop', handleDrop);
imageInput.addEventListener('change', handleImageSelect);
analyzeBtn.addEventListener('click', analyzeImage);
newAnalysisBtn.addEventListener('click', resetAnalysis);

// Handle drag and drop events
function handleDragOver(e) {
    e.preventDefault();
    uploadArea.classList.add('dragover');
}

function handleDragLeave(e) {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
}

function handleDrop(e) {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        handleFile(files[0]);
    }
}

// Handle file selection from input
function handleImageSelect(e) {
    const file = e.target.files[0];
    if (file) {
        handleFile(file);
    }
}

// Process selected file
function handleFile(file) {
    if (!file.type.startsWith('image/')) {
        alert('Please select an image file');
        return;
    }

    if (file.size > 5 * 1024 * 1024) {
        alert('File too large. Max 5MB');
        return;
    }

    const reader = new FileReader();
    reader.onload = function(e) {
        imagePreview.src = e.target.result;
        resultsSection.style.display = 'block';
        loadingSection.style.display = 'none';
    };
    reader.readAsDataURL(file);
}

// Reset analysis
function resetAnalysis() {
    imageInput.value = '';
    resultsSection.style.display = 'none';
    imagePreview.src = '';
    resultText.textContent = 'Weed Type';
    resultText.className = '';
    confidenceFill.style.width = '0%';
    confidenceText.textContent = '0%';

    // Hide detailed results
    const weedInfo = document.getElementById('weedInfo');
    if (weedInfo) {
        weedInfo.style.display = 'none';
    }
}

// Analyze image using the ML model
async function analyzeImage() {
    loadingSection.style.display = 'block';
    resultsSection.style.display = 'none';

    try {
        const formData = new FormData();
        const file = imageInput.files[0];
        formData.append('image', file);

        const response = await fetch('http://localhost:5000/analyze', {
            method: 'POST',
            body: formData
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        displayResults(result);

    } catch (error) {
        console.error('Error:', error);
        alert('Error analyzing weed image. Make sure backend is running on port 5000.');
    } finally {
        loadingSection.style.display = 'none';
        resultsSection.style.display = 'block';
    }
}

// Display analysis results
function displayResults(result) {
    const isHealthy = result.prediction === 'Healthy';

    resultText.textContent = result.prediction;
    resultText.className = isHealthy ? 'healthy' : 'diseased';

    const confidence = Math.round(result.confidence * 100);
    confidenceFill.style.width = confidence + '%';
    confidenceText.textContent = confidence + '%';

    if (isHealthy) {
        confidenceFill.style.background = '#27ae60';
    } else {
        confidenceFill.style.background = '#e74c3c';
    }

    setTimeout(() => {
        confidenceFill.style.width = confidence + '%';
    }, 100);
}