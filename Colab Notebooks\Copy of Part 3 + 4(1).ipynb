{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [{"file_id": "15M6b0c7cZL-zUlprlN7PL8rgUv7J6B4B", "timestamp": 1753942617759}, {"file_id": "1V3DtNJa7TUFE1sY61QoRXxQeODUKsJwQ", "timestamp": 1753922534543}]}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "source": ["#Part 3\n"], "metadata": {"id": "kb5PZMV4zbNJ"}}, {"cell_type": "markdown", "source": ["##1. Set up and Installation\n"], "metadata": {"id": "WDH5x969i1jq"}}, {"cell_type": "code", "source": ["#1.\n", "# Install required packages\n", "!pip install tensorflow matplotlib seaborn pillow scikit-learn\n", "\n", "# Import libraries\n", "import os\n", "import zipfile\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from PIL import Image\n", "import tensorflow as tf\n", "from tensorflow import keras\n", "from tensorflow.keras import layers\n", "from tensorflow.keras.preprocessing.image import ImageDataGenerator\n", "from sklearn.model_selection import train_test_split\n", "from google.colab import files"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "eEvsAK-Ri2bh", "executionInfo": {"status": "ok", "timestamp": 1753938068145, "user_tz": -180, "elapsed": 23332, "user": {"displayName": "<PERSON><PERSON>", "userId": "10262468973935773382"}}, "outputId": "09760742-fe61-4bf0-ca2b-84376a310ca1"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Requirement already satisfied: tensorflow in /usr/local/lib/python3.11/dist-packages (2.18.0)\n", "Requirement already satisfied: matplotlib in /usr/local/lib/python3.11/dist-packages (3.10.0)\n", "Requirement already satisfied: seaborn in /usr/local/lib/python3.11/dist-packages (0.13.2)\n", "Requirement already satisfied: pillow in /usr/local/lib/python3.11/dist-packages (11.3.0)\n", "Requirement already satisfied: scikit-learn in /usr/local/lib/python3.11/dist-packages (1.6.1)\n", "Requirement already satisfied: absl-py>=1.0.0 in /usr/local/lib/python3.11/dist-packages (from tensorflow) (1.4.0)\n", "Requirement already satisfied: astunparse>=1.6.0 in /usr/local/lib/python3.11/dist-packages (from tensorflow) (1.6.3)\n", "Requirement already satisfied: flatbuffers>=24.3.25 in /usr/local/lib/python3.11/dist-packages (from tensorflow) (25.2.10)\n", "Requirement already satisfied: gast!=0.5.0,!=0.5.1,!=0.5.2,>=0.2.1 in /usr/local/lib/python3.11/dist-packages (from tensorflow) (0.6.0)\n", "Requirement already satisfied: google-pasta>=0.1.1 in /usr/local/lib/python3.11/dist-packages (from tensorflow) (0.2.0)\n", "Requirement already satisfied: libclang>=13.0.0 in /usr/local/lib/python3.11/dist-packages (from tensorflow) (18.1.1)\n", "Requirement already satisfied: opt-einsum>=2.3.2 in /usr/local/lib/python3.11/dist-packages (from tensorflow) (3.4.0)\n", "Requirement already satisfied: packaging in /usr/local/lib/python3.11/dist-packages (from tensorflow) (25.0)\n", "Requirement already satisfied: protobuf!=4.21.0,!=4.21.1,!=4.21.2,!=4.21.3,!=4.21.4,!=4.21.5,<6.0.0dev,>=3.20.3 in /usr/local/lib/python3.11/dist-packages (from tensorflow) (5.29.5)\n", "Requirement already satisfied: requests<3,>=2.21.0 in /usr/local/lib/python3.11/dist-packages (from tensorflow) (2.32.3)\n", "Requirement already satisfied: setuptools in /usr/local/lib/python3.11/dist-packages (from tensorflow) (75.2.0)\n", "Requirement already satisfied: six>=1.12.0 in /usr/local/lib/python3.11/dist-packages (from tensorflow) (1.17.0)\n", "Requirement already satisfied: termcolor>=1.1.0 in /usr/local/lib/python3.11/dist-packages (from tensorflow) (3.1.0)\n", "Requirement already satisfied: typing-extensions>=3.6.6 in /usr/local/lib/python3.11/dist-packages (from tensorflow) (4.14.1)\n", "Requirement already satisfied: wrapt>=1.11.0 in /usr/local/lib/python3.11/dist-packages (from tensorflow) (1.17.2)\n", "Requirement already satisfied: grpcio<2.0,>=1.24.3 in /usr/local/lib/python3.11/dist-packages (from tensorflow) (1.74.0)\n", "Requirement already satisfied: tensorboard<2.19,>=2.18 in /usr/local/lib/python3.11/dist-packages (from tensorflow) (2.18.0)\n", "Requirement already satisfied: keras>=3.5.0 in /usr/local/lib/python3.11/dist-packages (from tensorflow) (3.8.0)\n", "Requirement already satisfied: numpy<2.1.0,>=1.26.0 in /usr/local/lib/python3.11/dist-packages (from tensorflow) (2.0.2)\n", "Requirement already satisfied: h5py>=3.11.0 in /usr/local/lib/python3.11/dist-packages (from tensorflow) (3.14.0)\n", "Requirement already satisfied: ml-dtypes<0.5.0,>=0.4.0 in /usr/local/lib/python3.11/dist-packages (from tensorflow) (0.4.1)\n", "Requirement already satisfied: tensorflow-io-gcs-filesystem>=0.23.1 in /usr/local/lib/python3.11/dist-packages (from tensorflow) (0.37.1)\n", "Requirement already satisfied: contourpy>=1.0.1 in /usr/local/lib/python3.11/dist-packages (from matplotlib) (1.3.2)\n", "Requirement already satisfied: cycler>=0.10 in /usr/local/lib/python3.11/dist-packages (from matplotlib) (0.12.1)\n", "Requirement already satisfied: fonttools>=4.22.0 in /usr/local/lib/python3.11/dist-packages (from matplotlib) (4.59.0)\n", "Requirement already satisfied: kiwisolver>=1.3.1 in /usr/local/lib/python3.11/dist-packages (from matplotlib) (1.4.8)\n", "Requirement already satisfied: pyparsing>=2.3.1 in /usr/local/lib/python3.11/dist-packages (from matplotlib) (3.2.3)\n", "Requirement already satisfied: python-dateutil>=2.7 in /usr/local/lib/python3.11/dist-packages (from matplotlib) (2.9.0.post0)\n", "Requirement already satisfied: pandas>=1.2 in /usr/local/lib/python3.11/dist-packages (from seaborn) (2.2.2)\n", "Requirement already satisfied: scipy>=1.6.0 in /usr/local/lib/python3.11/dist-packages (from scikit-learn) (1.16.0)\n", "Requirement already satisfied: joblib>=1.2.0 in /usr/local/lib/python3.11/dist-packages (from scikit-learn) (1.5.1)\n", "Requirement already satisfied: threadpoolctl>=3.1.0 in /usr/local/lib/python3.11/dist-packages (from scikit-learn) (3.6.0)\n", "Requirement already satisfied: wheel<1.0,>=0.23.0 in /usr/local/lib/python3.11/dist-packages (from astunparse>=1.6.0->tensorflow) (0.45.1)\n", "Requirement already satisfied: rich in /usr/local/lib/python3.11/dist-packages (from keras>=3.5.0->tensorflow) (13.9.4)\n", "Requirement already satisfied: namex in /usr/local/lib/python3.11/dist-packages (from keras>=3.5.0->tensorflow) (0.1.0)\n", "Requirement already satisfied: optree in /usr/local/lib/python3.11/dist-packages (from keras>=3.5.0->tensorflow) (0.17.0)\n", "Requirement already satisfied: pytz>=2020.1 in /usr/local/lib/python3.11/dist-packages (from pandas>=1.2->seaborn) (2025.2)\n", "Requirement already satisfied: tzdata>=2022.7 in /usr/local/lib/python3.11/dist-packages (from pandas>=1.2->seaborn) (2025.2)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.11/dist-packages (from requests<3,>=2.21.0->tensorflow) (3.4.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.11/dist-packages (from requests<3,>=2.21.0->tensorflow) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.11/dist-packages (from requests<3,>=2.21.0->tensorflow) (2.5.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.11/dist-packages (from requests<3,>=2.21.0->tensorflow) (2025.7.14)\n", "Requirement already satisfied: markdown>=2.6.8 in /usr/local/lib/python3.11/dist-packages (from tensorboard<2.19,>=2.18->tensorflow) (3.8.2)\n", "Requirement already satisfied: tensorboard-data-server<0.8.0,>=0.7.0 in /usr/local/lib/python3.11/dist-packages (from tensorboard<2.19,>=2.18->tensorflow) (0.7.2)\n", "Requirement already satisfied: werkzeug>=1.0.1 in /usr/local/lib/python3.11/dist-packages (from tensorboard<2.19,>=2.18->tensorflow) (3.1.3)\n", "Requirement already satisfied: MarkupSafe>=2.1.1 in /usr/local/lib/python3.11/dist-packages (from werkzeug>=1.0.1->tensorboard<2.19,>=2.18->tensorflow) (3.0.2)\n", "Requirement already satisfied: markdown-it-py>=2.2.0 in /usr/local/lib/python3.11/dist-packages (from rich->keras>=3.5.0->tensorflow) (3.0.0)\n", "Requirement already satisfied: pygments<3.0.0,>=2.13.0 in /usr/local/lib/python3.11/dist-packages (from rich->keras>=3.5.0->tensorflow) (2.19.2)\n", "Requirement already satisfied: mdurl~=0.1 in /usr/local/lib/python3.11/dist-packages (from markdown-it-py>=2.2.0->rich->keras>=3.5.0->tensorflow) (0.1.2)\n"]}]}, {"cell_type": "markdown", "source": ["2. Data Loading & Preparation\n"], "metadata": {"id": "vE5Ua3e2hQjZ"}}, {"cell_type": "markdown", "source": ["2.1 Uploading and Extraction of the Dataset\n"], "metadata": {"id": "dhWFw5HuhSMp"}}, {"cell_type": "code", "source": ["# The Uploading of the zipped files(which contain the dataset)\n", "# Upload and extract Diseased.zip\n", "print(\"⬆️ Upload 'Diseased.zip'\")\n", "uploaded = files.upload()\n", "with zipfile.ZipFile(\"Diseased.zip\", 'r') as zip_ref:\n", "    zip_ref.extractall(\"Diseased\")\n", "\n", "# Upload and extract Healthy.zip\n", "print(\"⬆️ Upload 'Healthy.zip'\")\n", "uploaded = files.upload()\n", "with zipfile.ZipFile(\"Healthy.zip\", 'r') as zip_ref:\n", "    zip_ref.extractall(\"Healthy\")\n", "\n", "# Define paths\n", "diseased_dir = \"Diseased/Diseased\"\n", "healthy_dir = \"Healthy/Healthy\""], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 167}, "id": "_G1ujzXFzFC3", "executionInfo": {"status": "ok", "timestamp": 1753938098129, "user_tz": -180, "elapsed": 29976, "user": {"displayName": "<PERSON><PERSON>", "userId": "10262468973935773382"}}, "outputId": "83e3f000-2358-4963-93e1-374fd291af9b"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["⬆️ Upload 'Diseased.zip'\n"]}, {"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["\n", "     <input type=\"file\" id=\"files-5b17552d-b95e-4b96-8628-811015df90b5\" name=\"files[]\" multiple disabled\n", "        style=\"border:none\" />\n", "     <output id=\"result-5b17552d-b95e-4b96-8628-811015df90b5\">\n", "      Upload widget is only available when the cell has been executed in the\n", "      current browser session. Please rerun this cell to enable.\n", "      </output>\n", "      <script>// Copyright 2017 Google LLC\n", "//\n", "// Licensed under the Apache License, Version 2.0 (the \"License\");\n", "// you may not use this file except in compliance with the License.\n", "// You may obtain a copy of the License at\n", "//\n", "//      http://www.apache.org/licenses/LICENSE-2.0\n", "//\n", "// Unless required by applicable law or agreed to in writing, software\n", "// distributed under the License is distributed on an \"AS IS\" BASIS,\n", "// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n", "// See the License for the specific language governing permissions and\n", "// limitations under the License.\n", "\n", "/**\n", " * @fileoverview Helpers for google.colab Python module.\n", " */\n", "(function(scope) {\n", "function span(text, styleAttributes = {}) {\n", "  const element = document.createElement('span');\n", "  element.textContent = text;\n", "  for (const key of Object.keys(styleAttributes)) {\n", "    element.style[key] = styleAttributes[key];\n", "  }\n", "  return element;\n", "}\n", "\n", "// Max number of bytes which will be uploaded at a time.\n", "const MAX_PAYLOAD_SIZE = 100 * 1024;\n", "\n", "function _uploadFiles(inputId, outputId) {\n", "  const steps = uploadFilesStep(inputId, outputId);\n", "  const outputElement = document.getElementById(outputId);\n", "  // Cache steps on the outputElement to make it available for the next call\n", "  // to uploadFilesContinue from Python.\n", "  outputElement.steps = steps;\n", "\n", "  return _uploadFilesContinue(outputId);\n", "}\n", "\n", "// This is roughly an async generator (not supported in the browser yet),\n", "// where there are multiple asynchronous steps and the Python side is going\n", "// to poll for completion of each step.\n", "// This uses a Promise to block the python side on completion of each step,\n", "// then passes the result of the previous step as the input to the next step.\n", "function _uploadFilesContinue(outputId) {\n", "  const outputElement = document.getElementById(outputId);\n", "  const steps = outputElement.steps;\n", "\n", "  const next = steps.next(outputElement.lastPromiseValue);\n", "  return Promise.resolve(next.value.promise).then((value) => {\n", "    // Cache the last promise value to make it available to the next\n", "    // step of the generator.\n", "    outputElement.lastPromiseValue = value;\n", "    return next.value.response;\n", "  });\n", "}\n", "\n", "/**\n", " * Generator function which is called between each async step of the upload\n", " * process.\n", " * @param {string} inputId Element ID of the input file picker element.\n", " * @param {string} outputId Element ID of the output display.\n", " * @return {!Iterable<!Object>} Iterable of next steps.\n", " */\n", "function* uploadFilesStep(inputId, outputId) {\n", "  const inputElement = document.getElementById(inputId);\n", "  inputElement.disabled = false;\n", "\n", "  const outputElement = document.getElementById(outputId);\n", "  outputElement.innerHTML = '';\n", "\n", "  const pickedPromise = new Promise((resolve) => {\n", "    inputElement.addEventListener('change', (e) => {\n", "      resolve(e.target.files);\n", "    });\n", "  });\n", "\n", "  const cancel = document.createElement('button');\n", "  inputElement.parentElement.appendChild(cancel);\n", "  cancel.textContent = 'Cancel upload';\n", "  const cancelPromise = new Promise((resolve) => {\n", "    cancel.onclick = () => {\n", "      resolve(null);\n", "    };\n", "  });\n", "\n", "  // Wait for the user to pick the files.\n", "  const files = yield {\n", "    promise: Promise.race([pickedPromise, cancelPromise]),\n", "    response: {\n", "      action: 'starting',\n", "    }\n", "  };\n", "\n", "  cancel.remove();\n", "\n", "  // Disable the input element since further picks are not allowed.\n", "  inputElement.disabled = true;\n", "\n", "  if (!files) {\n", "    return {\n", "      response: {\n", "        action: 'complete',\n", "      }\n", "    };\n", "  }\n", "\n", "  for (const file of files) {\n", "    const li = document.createElement('li');\n", "    li.append(span(file.name, {fontWeight: 'bold'}));\n", "    li.append(span(\n", "        `(${file.type || 'n/a'}) - ${file.size} bytes, ` +\n", "        `last modified: ${\n", "            file.lastModifiedDate ? file.lastModifiedDate.toLocaleDateString() :\n", "                                    'n/a'} - `));\n", "    const percent = span('0% done');\n", "    li.append<PERSON><PERSON>d(percent);\n", "\n", "    outputElement.appendChild(li);\n", "\n", "    const fileDataPromise = new Promise((resolve) => {\n", "      const reader = new FileReader();\n", "      reader.onload = (e) => {\n", "        resolve(e.target.result);\n", "      };\n", "      reader.readAsArrayBuffer(file);\n", "    });\n", "    // Wait for the data to be ready.\n", "    let fileData = yield {\n", "      promise: fileDataPromise,\n", "      response: {\n", "        action: 'continue',\n", "      }\n", "    };\n", "\n", "    // Use a chunked sending to avoid message size limits. See b/62115660.\n", "    let position = 0;\n", "    do {\n", "      const length = Math.min(fileData.byteLength - position, MAX_PAYLOAD_SIZE);\n", "      const chunk = new Uint8Array(fileData, position, length);\n", "      position += length;\n", "\n", "      const base64 = btoa(String.fromCharCode.apply(null, chunk));\n", "      yield {\n", "        response: {\n", "          action: 'append',\n", "          file: file.name,\n", "          data: base64,\n", "        },\n", "      };\n", "\n", "      let percentDone = fileData.byteLength === 0 ?\n", "          100 :\n", "          Math.round((position / fileData.byteLength) * 100);\n", "      percent.textContent = `${percentDone}% done`;\n", "\n", "    } while (position < fileData.byteLength);\n", "  }\n", "\n", "  // All done.\n", "  yield {\n", "    response: {\n", "      action: 'complete',\n", "    }\n", "  };\n", "}\n", "\n", "scope.google = scope.google || {};\n", "scope.google.colab = scope.google.colab || {};\n", "scope.google.colab._files = {\n", "  _uploadFiles,\n", "  _uploadFilesContinue,\n", "};\n", "})(self);\n", "</script> "]}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["Saving Diseased.zip to Diseased.zip\n", "⬆️ Upload 'Healthy.zip'\n"]}, {"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["\n", "     <input type=\"file\" id=\"files-8970f050-5132-47ab-92a2-ca91f375099e\" name=\"files[]\" multiple disabled\n", "        style=\"border:none\" />\n", "     <output id=\"result-8970f050-5132-47ab-92a2-ca91f375099e\">\n", "      Upload widget is only available when the cell has been executed in the\n", "      current browser session. Please rerun this cell to enable.\n", "      </output>\n", "      <script>// Copyright 2017 Google LLC\n", "//\n", "// Licensed under the Apache License, Version 2.0 (the \"License\");\n", "// you may not use this file except in compliance with the License.\n", "// You may obtain a copy of the License at\n", "//\n", "//      http://www.apache.org/licenses/LICENSE-2.0\n", "//\n", "// Unless required by applicable law or agreed to in writing, software\n", "// distributed under the License is distributed on an \"AS IS\" BASIS,\n", "// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n", "// See the License for the specific language governing permissions and\n", "// limitations under the License.\n", "\n", "/**\n", " * @fileoverview Helpers for google.colab Python module.\n", " */\n", "(function(scope) {\n", "function span(text, styleAttributes = {}) {\n", "  const element = document.createElement('span');\n", "  element.textContent = text;\n", "  for (const key of Object.keys(styleAttributes)) {\n", "    element.style[key] = styleAttributes[key];\n", "  }\n", "  return element;\n", "}\n", "\n", "// Max number of bytes which will be uploaded at a time.\n", "const MAX_PAYLOAD_SIZE = 100 * 1024;\n", "\n", "function _uploadFiles(inputId, outputId) {\n", "  const steps = uploadFilesStep(inputId, outputId);\n", "  const outputElement = document.getElementById(outputId);\n", "  // Cache steps on the outputElement to make it available for the next call\n", "  // to uploadFilesContinue from Python.\n", "  outputElement.steps = steps;\n", "\n", "  return _uploadFilesContinue(outputId);\n", "}\n", "\n", "// This is roughly an async generator (not supported in the browser yet),\n", "// where there are multiple asynchronous steps and the Python side is going\n", "// to poll for completion of each step.\n", "// This uses a Promise to block the python side on completion of each step,\n", "// then passes the result of the previous step as the input to the next step.\n", "function _uploadFilesContinue(outputId) {\n", "  const outputElement = document.getElementById(outputId);\n", "  const steps = outputElement.steps;\n", "\n", "  const next = steps.next(outputElement.lastPromiseValue);\n", "  return Promise.resolve(next.value.promise).then((value) => {\n", "    // Cache the last promise value to make it available to the next\n", "    // step of the generator.\n", "    outputElement.lastPromiseValue = value;\n", "    return next.value.response;\n", "  });\n", "}\n", "\n", "/**\n", " * Generator function which is called between each async step of the upload\n", " * process.\n", " * @param {string} inputId Element ID of the input file picker element.\n", " * @param {string} outputId Element ID of the output display.\n", " * @return {!Iterable<!Object>} Iterable of next steps.\n", " */\n", "function* uploadFilesStep(inputId, outputId) {\n", "  const inputElement = document.getElementById(inputId);\n", "  inputElement.disabled = false;\n", "\n", "  const outputElement = document.getElementById(outputId);\n", "  outputElement.innerHTML = '';\n", "\n", "  const pickedPromise = new Promise((resolve) => {\n", "    inputElement.addEventListener('change', (e) => {\n", "      resolve(e.target.files);\n", "    });\n", "  });\n", "\n", "  const cancel = document.createElement('button');\n", "  inputElement.parentElement.appendChild(cancel);\n", "  cancel.textContent = 'Cancel upload';\n", "  const cancelPromise = new Promise((resolve) => {\n", "    cancel.onclick = () => {\n", "      resolve(null);\n", "    };\n", "  });\n", "\n", "  // Wait for the user to pick the files.\n", "  const files = yield {\n", "    promise: Promise.race([pickedPromise, cancelPromise]),\n", "    response: {\n", "      action: 'starting',\n", "    }\n", "  };\n", "\n", "  cancel.remove();\n", "\n", "  // Disable the input element since further picks are not allowed.\n", "  inputElement.disabled = true;\n", "\n", "  if (!files) {\n", "    return {\n", "      response: {\n", "        action: 'complete',\n", "      }\n", "    };\n", "  }\n", "\n", "  for (const file of files) {\n", "    const li = document.createElement('li');\n", "    li.append(span(file.name, {fontWeight: 'bold'}));\n", "    li.append(span(\n", "        `(${file.type || 'n/a'}) - ${file.size} bytes, ` +\n", "        `last modified: ${\n", "            file.lastModifiedDate ? file.lastModifiedDate.toLocaleDateString() :\n", "                                    'n/a'} - `));\n", "    const percent = span('0% done');\n", "    li.append<PERSON><PERSON>d(percent);\n", "\n", "    outputElement.appendChild(li);\n", "\n", "    const fileDataPromise = new Promise((resolve) => {\n", "      const reader = new FileReader();\n", "      reader.onload = (e) => {\n", "        resolve(e.target.result);\n", "      };\n", "      reader.readAsArrayBuffer(file);\n", "    });\n", "    // Wait for the data to be ready.\n", "    let fileData = yield {\n", "      promise: fileDataPromise,\n", "      response: {\n", "        action: 'continue',\n", "      }\n", "    };\n", "\n", "    // Use a chunked sending to avoid message size limits. See b/62115660.\n", "    let position = 0;\n", "    do {\n", "      const length = Math.min(fileData.byteLength - position, MAX_PAYLOAD_SIZE);\n", "      const chunk = new Uint8Array(fileData, position, length);\n", "      position += length;\n", "\n", "      const base64 = btoa(String.fromCharCode.apply(null, chunk));\n", "      yield {\n", "        response: {\n", "          action: 'append',\n", "          file: file.name,\n", "          data: base64,\n", "        },\n", "      };\n", "\n", "      let percentDone = fileData.byteLength === 0 ?\n", "          100 :\n", "          Math.round((position / fileData.byteLength) * 100);\n", "      percent.textContent = `${percentDone}% done`;\n", "\n", "    } while (position < fileData.byteLength);\n", "  }\n", "\n", "  // All done.\n", "  yield {\n", "    response: {\n", "      action: 'complete',\n", "    }\n", "  };\n", "}\n", "\n", "scope.google = scope.google || {};\n", "scope.google.colab = scope.google.colab || {};\n", "scope.google.colab._files = {\n", "  _uploadFiles,\n", "  _uploadFilesContinue,\n", "};\n", "})(self);\n", "</script> "]}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["Saving Healthy.zip to Healthy.zip\n"]}]}, {"cell_type": "markdown", "source": ["2.2 Creating the Metadata DataFrame\n"], "metadata": {"id": "7B_qoL6fhch-"}}, {"cell_type": "code", "source": ["# Creation of the metadata frame\n", "def load_metadata(folder, label):\n", "    \"\"\"Load image metadata (filename, class, dimensions)\"\"\"\n", "    data = []\n", "    for file in os.listdir(folder):\n", "        if file.lower().endswith(('.png', '.jpg', '.jpeg')):\n", "            path = os.path.join(folder, file)\n", "            try:\n", "                with Image.open(path) as img:\n", "                    data.append({\n", "                        \"filename\": path,\n", "                        \"class\": label,\n", "                        \"width\": img.width,\n", "                        \"height\": img.height\n", "                    })\n", "            except:\n", "                continue\n", "    return data\n", "\n", "# Combine metadata\n", "df = pd.DataFrame(load_metadata(diseased_dir, \"Diseased\") +\n", "                  load_metadata(healthy_dir, \"Healthy\"))\n", "\n", "# Display summary\n", "print(\"📊 Dataset Summary:\")\n", "print(df.groupby(\"class\").agg(\n", "    count=(\"filename\", \"count\"),\n", "    avg_width=(\"width\", \"mean\"),\n", "    avg_height=(\"height\", \"mean\")\n", "))\n", "\n", "# Plot class distribution\n", "plt.figure(figsize=(6,4))\n", "sns.countplot(data=df, x=\"class\", hue=\"class\", palette={\"Diseased\": \"red\", \"Healthy\": \"green\"}, legend=False)\n", "plt.title(\"Class Distribution\")\n", "plt.grid(True)\n", "plt.show()"], "metadata": {"id": "BG5gO67izFzP"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["3. Data Preprocessing\n", "\n", "   3.1 Image Augmentation & Generators\n"], "metadata": {"id": "GSYve5m4g0h0"}}, {"cell_type": "code", "source": ["#Image generation\n", "\n", "# Parameters\n", "IMG_SIZE = (256, 256)\n", "BATCH_SIZE = 32\n", "\n", "# Data augmentation for training\n", "train_datagen = ImageDataGenerator(\n", "    rescale=1./255,\n", "    rotation_range=20,\n", "    width_shift_range=0.2,\n", "    height_shift_range=0.2,\n", "    shear_range=0.2,\n", "    zoom_range=0.2,\n", "    horizontal_flip=True,\n", "    validation_split=0.2  # 20% for validation\n", ")\n", "\n", "# Generator for training data\n", "train_generator = train_datagen.flow_from_dataframe(\n", "    df,\n", "    x_col=\"filename\",\n", "    y_col=\"class\",\n", "    target_size=IMG_SIZE,\n", "    batch_size=BATCH_SIZE,\n", "    class_mode=\"binary\",\n", "    subset=\"training\"\n", ")\n", "\n", "# Generator for validation data\n", "val_generator = train_datagen.flow_from_dataframe(\n", "    df,\n", "    x_col=\"filename\",\n", "    y_col=\"class\",\n", "    target_size=IMG_SIZE,\n", "    batch_size=BATCH_SIZE,\n", "    class_mode=\"binary\",\n", "    subset=\"validation\"\n", ")"], "metadata": {"id": "mWMUEXOBz9xx", "executionInfo": {"status": "ok", "timestamp": 1753938098599, "user_tz": -180, "elapsed": 22, "user": {"displayName": "<PERSON><PERSON>", "userId": "10262468973935773382"}}, "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "02cba263-ad39-45b7-9d26-56d7cd67eb1f"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Found 96 validated image filenames belonging to 2 classes.\n", "Found 24 validated image filenames belonging to 2 classes.\n"]}]}, {"cell_type": "markdown", "source": ["4.  Building the Model"], "metadata": {"id": "s-pSX5muhHuJ"}}, {"cell_type": "markdown", "source": ["4.1 CNN Architecture\n"], "metadata": {"id": "1h0N18djhDoc"}}, {"cell_type": "code", "source": ["def build_model():\n", "    model = keras.Sequential([\n", "        layers.Input(shape=(*IMG_SIZE, 3)),  # Explicit Input layer\n", "        layers.Conv2D(32, (3,3), activation='relu'),  # Remove input_shape from here\n", "        layers.MaxPooling2D((2,2)),\n", "        layers.Conv2D(64, (3,3), activation='relu'),\n", "        layers.MaxPooling2D((2,2)),\n", "        layers.Conv2D(128, (3,3), activation='relu'),\n", "        layers.MaxPooling2D((2,2)),\n", "        layers.<PERSON><PERSON>(),\n", "        layers.Dense(128, activation='relu'),\n", "        layers.Dropout(0.5),\n", "        layers.Dense(1, activation='sigmoid')  # Binary classification\n", "    ])\n", "\n", "    model.compile(\n", "        optimizer='adam',\n", "        loss='binary_crossentropy',\n", "        metrics=['accuracy', tf.keras.metrics.Precision(), tf.keras.metrics.Recall()]\n", "    )\n", "    return model\n", "\n", "model = build_model()\n", "model.summary()"], "metadata": {"id": "v-kb2FtO0TS-", "executionInfo": {"status": "ok", "timestamp": 1753938098877, "user_tz": -180, "elapsed": 275, "user": {"displayName": "<PERSON><PERSON>", "userId": "10262468973935773382"}}, "colab": {"base_uri": "https://localhost:8080/", "height": 449}, "outputId": "7de233d3-79ae-4566-9fc4-9094b46c62c0"}, "execution_count": null, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["\u001b[1mModel: \"sequential\"\u001b[0m\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">Model: \"sequential\"</span>\n", "</pre>\n"]}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━┓\n", "┃\u001b[1m \u001b[0m\u001b[1mLayer (type)                   \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mOutput Shape          \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1m      Param #\u001b[0m\u001b[1m \u001b[0m┃\n", "┡━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━┩\n", "│ conv2d (\u001b[38;5;33mConv2D\u001b[0m)                 │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m254\u001b[0m, \u001b[38;5;34m254\u001b[0m, \u001b[38;5;34m32\u001b[0m)   │           \u001b[38;5;34m896\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ max_pooling2d (\u001b[38;5;33mMaxPooling2D\u001b[0m)    │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m127\u001b[0m, \u001b[38;5;34m127\u001b[0m, \u001b[38;5;34m32\u001b[0m)   │             \u001b[38;5;34m0\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ conv2d_1 (\u001b[38;5;33mConv2D\u001b[0m)               │ (\u001b[38;5;45mN<PERSON>\u001b[0m, \u001b[38;5;34m125\u001b[0m, \u001b[38;5;34m125\u001b[0m, \u001b[38;5;34m64\u001b[0m)   │        \u001b[38;5;34m18,496\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ max_pooling2d_1 (\u001b[38;5;33mMaxPooling2D\u001b[0m)  │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m62\u001b[0m, \u001b[38;5;34m62\u001b[0m, \u001b[38;5;34m64\u001b[0m)     │             \u001b[38;5;34m0\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ conv2d_2 (\u001b[38;5;33mConv2D\u001b[0m)               │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m60\u001b[0m, \u001b[38;5;34m60\u001b[0m, \u001b[38;5;34m128\u001b[0m)    │        \u001b[38;5;34m73,856\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ max_pooling2d_2 (\u001b[38;5;33mMaxPooling2D\u001b[0m)  │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m30\u001b[0m, \u001b[38;5;34m30\u001b[0m, \u001b[38;5;34m128\u001b[0m)    │             \u001b[38;5;34m0\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ flatten (\u001b[38;5;33m<PERSON><PERSON><PERSON>\u001b[0m)               │ (\u001b[38;5;45m<PERSON>one\u001b[0m, \u001b[38;5;34m115200\u001b[0m)         │             \u001b[38;5;34m0\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ dense (\u001b[38;5;33mDense\u001b[0m)                   │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m128\u001b[0m)            │    \u001b[38;5;34m14,745,728\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ dropout (\u001b[38;5;33mDropout\u001b[0m)               │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m128\u001b[0m)            │             \u001b[38;5;34m0\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ dense_1 (\u001b[38;5;33mDense\u001b[0m)                 │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m1\u001b[0m)              │           \u001b[38;5;34m129\u001b[0m │\n", "└─────────────────────────────────┴────────────────────────┴───────────────┘\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━┓\n", "┃<span style=\"font-weight: bold\"> Layer (type)                    </span>┃<span style=\"font-weight: bold\"> Output Shape           </span>┃<span style=\"font-weight: bold\">       Param # </span>┃\n", "┡━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━┩\n", "│ conv2d (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Conv2D</span>)                 │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">254</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">254</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">32</span>)   │           <span style=\"color: #00af00; text-decoration-color: #00af00\">896</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ max_pooling2d (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">MaxPooling2D</span>)    │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">127</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">127</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">32</span>)   │             <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ conv2d_1 (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Conv2D</span>)               │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">125</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">125</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">64</span>)   │        <span style=\"color: #00af00; text-decoration-color: #00af00\">18,496</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ max_pooling2d_1 (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">MaxPooling2D</span>)  │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">62</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">62</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">64</span>)     │             <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ conv2d_2 (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Conv2D</span>)               │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">60</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">60</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">128</span>)    │        <span style=\"color: #00af00; text-decoration-color: #00af00\">73,856</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ max_pooling2d_2 (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">MaxPooling2D</span>)  │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">30</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">30</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">128</span>)    │             <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ flatten (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Flatten</span>)               │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">115200</span>)         │             <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ dense (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Dense</span>)                   │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">128</span>)            │    <span style=\"color: #00af00; text-decoration-color: #00af00\">14,745,728</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ dropout (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Dropout</span>)               │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">128</span>)            │             <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ dense_1 (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Dense</span>)                 │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">1</span>)              │           <span style=\"color: #00af00; text-decoration-color: #00af00\">129</span> │\n", "└─────────────────────────────────┴────────────────────────┴───────────────┘\n", "</pre>\n"]}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["\u001b[1m Total params: \u001b[0m\u001b[38;5;34m14,839,105\u001b[0m (56.61 MB)\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\"> Total params: </span><span style=\"color: #00af00; text-decoration-color: #00af00\">14,839,105</span> (56.61 MB)\n", "</pre>\n"]}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["\u001b[1m Trainable params: \u001b[0m\u001b[38;5;34m14,839,105\u001b[0m (56.61 MB)\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\"> Trainable params: </span><span style=\"color: #00af00; text-decoration-color: #00af00\">14,839,105</span> (56.61 MB)\n", "</pre>\n"]}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["\u001b[1m Non-trainable params: \u001b[0m\u001b[38;5;34m0\u001b[0m (0.00 B)\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\"> Non-trainable params: </span><span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> (0.00 B)\n", "</pre>\n"]}, "metadata": {}}]}, {"cell_type": "markdown", "source": ["4.2 Training"], "metadata": {"id": "k7KME1tf0pQS"}}, {"cell_type": "code", "source": ["# Training the model\n", "history = model.fit(\n", "    train_generator,\n", "    validation_data=val_generator,\n", "    epochs=15,\n", "    steps_per_epoch=len(train_generator),\n", "    validation_steps=len(val_generator),\n", "    callbacks=[\n", "        keras.callbacks.EarlyStopping(patience=3, restore_best_weights=True),\n", "        keras.callbacks.ReduceLROnPlateau(factor=0.1, patience=2)\n", "    ]\n", ")"], "metadata": {"id": "c8WSA1IM0ths", "executionInfo": {"status": "ok", "timestamp": 1753938375564, "user_tz": -180, "elapsed": 276690, "user": {"displayName": "<PERSON><PERSON>", "userId": "10262468973935773382"}}, "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "b5ac9dbf-6f83-427b-e7ef-446038616faf"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.11/dist-packages/keras/src/trainers/data_adapters/py_dataset_adapter.py:121: UserWarning: Your `PyDataset` class should call `super().__init__(**kwargs)` in its constructor. `**kwargs` can include `workers`, `use_multiprocessing`, `max_queue_size`. Do not pass these arguments to `fit()`, as they will be ignored.\n", "  self._warn_if_super_not_called()\n"]}, {"output_type": "stream", "name": "stdout", "text": ["Epoch 1/15\n", "\u001b[1m3/3\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m32s\u001b[0m 7s/step - accuracy: 0.4714 - loss: 1.4599 - precision: 0.7943 - recall: 0.5222 - val_accuracy: 1.0000 - val_loss: 0.5738 - val_precision: 0.0000e+00 - val_recall: 0.0000e+00 - learning_rate: 0.0010\n", "Epoch 2/15\n", "\u001b[1m3/3\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m18s\u001b[0m 6s/step - accuracy: 0.5299 - loss: 0.7199 - precision: 0.7600 - recall: 0.5989 - val_accuracy: 0.0000e+00 - val_loss: 1.0701 - val_precision: 0.0000e+00 - val_recall: 0.0000e+00 - learning_rate: 0.0010\n", "Epoch 3/15\n", "\u001b[1m3/3\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m19s\u001b[0m 6s/step - accuracy: 0.7812 - loss: 0.4113 - precision: 0.7777 - recall: 1.0000 - val_accuracy: 1.0000 - val_loss: 0.4690 - val_precision: 0.0000e+00 - val_recall: 0.0000e+00 - learning_rate: 0.0010\n", "Epoch 4/15\n", "\u001b[1m3/3\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m16s\u001b[0m 6s/step - accuracy: 0.9948 - loss: 0.2282 - precision: 0.9932 - recall: 1.0000 - val_accuracy: 0.7917 - val_loss: 0.5215 - val_precision: 0.0000e+00 - val_recall: 0.0000e+00 - learning_rate: 0.0010\n", "Epoch 5/15\n", "\u001b[1m3/3\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m18s\u001b[0m 6s/step - accuracy: 0.9688 - loss: 0.1209 - precision: 0.9670 - recall: 0.9931 - val_accuracy: 1.0000 - val_loss: 0.1042 - val_precision: 0.0000e+00 - val_recall: 0.0000e+00 - learning_rate: 0.0010\n", "Epoch 6/15\n", "\u001b[1m3/3\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m20s\u001b[0m 6s/step - accuracy: 1.0000 - loss: 0.0642 - precision: 1.0000 - recall: 1.0000 - val_accuracy: 1.0000 - val_loss: 0.0787 - val_precision: 0.0000e+00 - val_recall: 0.0000e+00 - learning_rate: 0.0010\n", "Epoch 7/15\n", "\u001b[1m3/3\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m17s\u001b[0m 6s/step - accuracy: 1.0000 - loss: 0.0225 - precision: 1.0000 - recall: 1.0000 - val_accuracy: 1.0000 - val_loss: 0.0064 - val_precision: 0.0000e+00 - val_recall: 0.0000e+00 - learning_rate: 0.0010\n", "Epoch 8/15\n", "\u001b[1m3/3\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m17s\u001b[0m 5s/step - accuracy: 1.0000 - loss: 0.0193 - precision: 1.0000 - recall: 1.0000 - val_accuracy: 1.0000 - val_loss: 0.0143 - val_precision: 0.0000e+00 - val_recall: 0.0000e+00 - learning_rate: 0.0010\n", "Epoch 9/15\n", "\u001b[1m3/3\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m19s\u001b[0m 6s/step - accuracy: 0.9831 - loss: 0.0279 - precision: 0.9779 - recall: 1.0000 - val_accuracy: 1.0000 - val_loss: 0.0011 - val_precision: 0.0000e+00 - val_recall: 0.0000e+00 - learning_rate: 0.0010\n", "Epoch 10/15\n", "\u001b[1m3/3\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m21s\u001b[0m 6s/step - accuracy: 0.9909 - loss: 0.0195 - precision: 1.0000 - recall: 0.9875 - val_accuracy: 0.9167 - val_loss: 0.0941 - val_precision: 0.0000e+00 - val_recall: 0.0000e+00 - learning_rate: 0.0010\n", "Epoch 11/15\n", "\u001b[1m3/3\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m20s\u001b[0m 5s/step - accuracy: 0.9674 - loss: 0.0924 - precision: 0.9587 - recall: 1.0000 - val_accuracy: 1.0000 - val_loss: 5.7282e-04 - val_precision: 0.0000e+00 - val_recall: 0.0000e+00 - learning_rate: 0.0010\n", "Epoch 12/15\n", "\u001b[1m3/3\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m17s\u001b[0m 6s/step - accuracy: 0.9557 - loss: 0.0964 - precision: 1.0000 - recall: 0.9410 - val_accuracy: 1.0000 - val_loss: 7.0855e-04 - val_precision: 0.0000e+00 - val_recall: 0.0000e+00 - learning_rate: 0.0010\n", "Epoch 13/15\n", "\u001b[1m3/3\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m19s\u001b[0m 6s/step - accuracy: 0.9896 - loss: 0.0363 - precision: 0.9865 - recall: 1.0000 - val_accuracy: 0.4167 - val_loss: 2.1486 - val_precision: 0.0000e+00 - val_recall: 0.0000e+00 - learning_rate: 0.0010\n", "Epoch 14/15\n", "\u001b[1m3/3\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m22s\u001b[0m 5s/step - accuracy: 0.9206 - loss: 0.3087 - precision: 0.9049 - recall: 1.0000 - val_accuracy: 0.6250 - val_loss: 1.3035 - val_precision: 0.0000e+00 - val_recall: 0.0000e+00 - learning_rate: 1.0000e-04\n"]}]}, {"cell_type": "markdown", "source": ["5. The evaluation & results\n"], "metadata": {"id": "0kYA_AY-k5UC"}}, {"cell_type": "markdown", "source": ["5.1 Training History\n"], "metadata": {"id": "iiUrzWAUlCqr"}}, {"cell_type": "code", "source": ["# Plot accuracy and loss\n", "plt.figure(figsize=(12,5))\n", "\n", "plt.subplot(1,2,1)\n", "plt.plot(history.history['accuracy'], label='Train Accuracy')\n", "plt.plot(history.history['val_accuracy'], label='Validation Accuracy')\n", "plt.title('Model Accuracy')\n", "plt.xlabel('Epoch')\n", "plt.ylabel('Accuracy')\n", "plt.legend()\n", "\n", "plt.subplot(1,2,2)\n", "plt.plot(history.history['loss'], label='Train Loss')\n", "plt.plot(history.history['val_loss'], label='Validation Loss')\n", "plt.title('Model Loss')\n", "plt.xlabel('Epoch')\n", "plt.ylabel('Loss')\n", "plt.legend()\n", "\n", "plt.tight_layout()\n", "plt.show()"], "metadata": {"id": "ozmnQfue0wmX", "executionInfo": {"status": "ok", "timestamp": 1753938376417, "user_tz": -180, "elapsed": 850, "user": {"displayName": "<PERSON><PERSON>", "userId": "10262468973935773382"}}, "colab": {"base_uri": "https://localhost:8080/", "height": 507}, "outputId": "9b00ceb2-1c9b-474f-b4e8-29937aa4e52e"}, "execution_count": null, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 1200x500 with 2 Axes>"], "image/png": "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\n"}, "metadata": {}}]}, {"cell_type": "markdown", "source": ["Saving the Model"], "metadata": {"id": "h1oz4E4KlfEo"}}, {"cell_type": "code", "source": ["# Save in native Keras format (.keras)\n", "model.save('plant_disease_model.keras')\n", "\n", "# Download for deployment\n", "from google.colab import files\n", "files.download('plant_disease_model.keras')"], "metadata": {"id": "aptqvwWQlhQX", "colab": {"base_uri": "https://localhost:8080/", "height": 34}, "executionInfo": {"status": "ok", "timestamp": 1753938376616, "user_tz": -180, "elapsed": 195, "user": {"displayName": "<PERSON><PERSON>", "userId": "10262468973935773382"}}, "outputId": "3972e19d-07b8-4c51-d0c9-9a7b591b0f24"}, "execution_count": null, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.Javascript object>"], "application/javascript": ["\n", "    async function download(id, filename, size) {\n", "      if (!google.colab.kernel.accessAllowed) {\n", "        return;\n", "      }\n", "      const div = document.createElement('div');\n", "      const label = document.createElement('label');\n", "      label.textContent = `Downloading \"${filename}\": `;\n", "      div.append<PERSON><PERSON>d(label);\n", "      const progress = document.createElement('progress');\n", "      progress.max = size;\n", "      div.append<PERSON><PERSON>d(progress);\n", "      document.body.appendChild(div);\n", "\n", "      const buffers = [];\n", "      let downloaded = 0;\n", "\n", "      const channel = await google.colab.kernel.comms.open(id);\n", "      // Send a message to notify the kernel that we're ready.\n", "      channel.send({})\n", "\n", "      for await (const message of channel.messages) {\n", "        // Send a message to notify the kernel that we're ready.\n", "        channel.send({})\n", "        if (message.buffers) {\n", "          for (const buffer of message.buffers) {\n", "            buffers.push(buffer);\n", "            downloaded += buffer.byteLength;\n", "            progress.value = downloaded;\n", "          }\n", "        }\n", "      }\n", "      const blob = new Blob(buffers, {type: 'application/binary'});\n", "      const a = document.createElement('a');\n", "      a.href = window.URL.createObjectURL(blob);\n", "      a.download = filename;\n", "      div.append<PERSON>hild(a);\n", "      a.click();\n", "      div.remove();\n", "    }\n", "  "]}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.Javascript object>"], "application/javascript": ["download(\"download_72bd9560-dd6d-491c-9527-fbeb134a51eb\", \"plant_disease_model.keras\", 178117087)"]}, "metadata": {}}]}, {"cell_type": "code", "source": [], "metadata": {"id": "igwJKszIzj3I"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["#Part 4: Evaluation and Fine Tuning"], "metadata": {"id": "IfIl_TgvzgJr"}}, {"cell_type": "markdown", "source": [], "metadata": {"id": "w4aflXaazvxo"}}, {"cell_type": "code", "source": ["from sklearn.metrics import classification_report, confusion_matrix, f1_score, accuracy_score, precision_score, recall_score, mean_squared_error\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "# Predict on validation data\n", "y_pred_probs = model.predict(val_generator)\n", "y_pred = (y_pred_probs > 0.5).astype(\"int32\").flatten()\n", "y_true = val_generator.classes\n", "\n", "# Compute metrics\n", "accuracy = accuracy_score(y_true, y_pred)\n", "precision = precision_score(y_true, y_pred)\n", "recall = recall_score(y_true, y_pred)\n", "f1 = f1_score(y_true, y_pred)\n", "rmse = np.sqrt(mean_squared_error(y_true, y_pred))\n", "\n", "# Print metrics\n", "print(\"📊 Evaluation Metrics:\")\n", "print(f\"✅ Accuracy: {accuracy:.4f}\")\n", "print(f\"🎯 Precision: {precision:.4f}\")\n", "print(f\"📥 Recall: {recall:.4f}\")\n", "print(f\"📌 F1 Score: {f1:.4f}\")\n", "print(f\"📉 RMSE: {rmse:.4f}\")\n"], "metadata": {"id": "nme0lTFwzmi8", "executionInfo": {"status": "ok", "timestamp": 1753938378487, "user_tz": -180, "elapsed": 1855, "user": {"displayName": "<PERSON><PERSON>", "userId": "10262468973935773382"}}, "outputId": "d82773b7-09ca-4ae4-fed0-af7e2969df3d", "colab": {"base_uri": "https://localhost:8080/"}}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m1s\u001b[0m 1s/step\n", "📊 Evaluation Metrics:\n", "✅ Accuracy: 1.0000\n", "🎯 Precision: 0.0000\n", "📥 Recall: 0.0000\n", "📌 F1 Score: 0.0000\n", "📉 RMSE: 0.0000\n"]}, {"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.11/dist-packages/sklearn/metrics/_classification.py:1565: UndefinedMetricWarning: Precision is ill-defined and being set to 0.0 due to no predicted samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, f\"{metric.capitalize()} is\", len(result))\n", "/usr/local/lib/python3.11/dist-packages/sklearn/metrics/_classification.py:1565: UndefinedMetricWarning: Recall is ill-defined and being set to 0.0 due to no true samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, f\"{metric.capitalize()} is\", len(result))\n", "/usr/local/lib/python3.11/dist-packages/sklearn/metrics/_classification.py:1565: UndefinedMetricWarning: F-score is ill-defined and being set to 0.0 due to no true nor predicted samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, f\"{metric.capitalize()} is\", len(result))\n"]}]}, {"cell_type": "markdown", "source": ["##Confusion matrix and classification report"], "metadata": {"id": "p7qxdbjx062f"}}, {"cell_type": "code", "source": ["import numpy as np\n", "\n", "# Predict labels for validation data\n", "y_pred_probs = model.predict(val_generator, steps=len(val_generator), verbose=1)\n", "y_pred = np.round(y_pred_probs).astype(int).flatten()\n", "\n", "# Get true labels\n", "y_true = val_generator.classes\n"], "metadata": {"id": "nCiafX6s1BUX", "executionInfo": {"status": "ok", "timestamp": 1753938380136, "user_tz": -180, "elapsed": 1654, "user": {"displayName": "<PERSON><PERSON>", "userId": "10262468973935773382"}}, "outputId": "55ec86c7-a06f-4b7a-a1a3-ac87a0ae297f", "colab": {"base_uri": "https://localhost:8080/"}}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m1s\u001b[0m 1s/step\n"]}]}, {"cell_type": "code", "source": ["import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "from sklearn.metrics import confusion_matrix\n", "\n", "# Confusion Matrix\n", "cm = confusion_matrix(y_true, y_pred, labels=[0, 1])\n", "\n", "plt.figure(figsize=(5,4))\n", "sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',\n", "            xticklabels=[\"Healthy\", \"Diseased\"],\n", "            yticklabels=[\"Healthy\", \"Diseased\"])\n", "plt.xlabel(\"Predicted Label\")\n", "plt.ylabel(\"True Label\")\n", "plt.title(\"Confusion Matrix\")\n", "plt.tight_layout()\n", "plt.show()\n"], "metadata": {"id": "Q7yf3JRX1Xgz", "executionInfo": {"status": "ok", "timestamp": 1753938380702, "user_tz": -180, "elapsed": 549, "user": {"displayName": "<PERSON><PERSON>", "userId": "10262468973935773382"}}, "outputId": "12aafadf-0f75-4aa9-fecc-6c6347674bea", "colab": {"base_uri": "https://localhost:8080/", "height": 407}}, "execution_count": null, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 500x400 with 2 Axes>"], "image/png": "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\n"}, "metadata": {}}]}, {"cell_type": "code", "source": ["from sklearn.metrics import classification_report, f1_score, mean_squared_error\n", "\n", "# Classification report\n", "print(\"\\n📋 Classification Report:\\n\")\n", "print(classification_report(\n", "    y_true, y_pred,\n", "    labels=[0, 1],\n", "    target_names=[\"Healthy\", \"Diseased\"],\n", "    zero_division=0\n", "))\n", "\n", "# Evaluation metrics\n", "acc = np.mean(y_true == y_pred)\n", "f1 = f1_score(y_true, y_pred, zero_division=0)\n", "\n", "# Calculate RMSE manually if squared=False is not available\n", "mse = mean_squared_error(y_true, y_pred)\n", "rmse = mse ** 0.5  # equivalent to squared=False\n", "\n", "print(f\"✅ Accuracy: {acc:.4f}\")\n", "print(f\"📌 F1 Score: {f1:.4f}\")\n", "print(f\"📉 RMSE: {rmse:.4f}\")\n"], "metadata": {"id": "_mJSqKzH1bmJ", "executionInfo": {"status": "ok", "timestamp": 1753938380733, "user_tz": -180, "elapsed": 24, "user": {"displayName": "<PERSON><PERSON>", "userId": "10262468973935773382"}}, "outputId": "5b49f8e6-3d25-4e84-f580-523c811b8125", "colab": {"base_uri": "https://localhost:8080/"}}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "📋 Classification Report:\n", "\n", "              precision    recall  f1-score   support\n", "\n", "     Healthy       1.00      1.00      1.00        24\n", "    Diseased       0.00      0.00      0.00         0\n", "\n", "    accuracy                           1.00        24\n", "   macro avg       0.50      0.50      0.50        24\n", "weighted avg       1.00      1.00      1.00        24\n", "\n", "✅ Accuracy: 1.0000\n", "📌 F1 Score: 0.0000\n", "📉 RMSE: 0.0000\n"]}]}, {"cell_type": "markdown", "source": ["##Fine Tuned Model"], "metadata": {"id": "jnnVrfny1xMB"}}, {"cell_type": "code", "source": ["def build_fine_tuned_model():\n", "    model = keras.Sequential([\n", "        layers.Input(shape=(*IMG_SIZE, 3)),\n", "        layers.Conv2D(32, (3, 3), activation='relu'),\n", "        layers.BatchNormalization(),\n", "        layers.MaxPooling2D((2, 2)),\n", "        layers.Conv2D(64, (3, 3), activation='relu'),\n", "        layers.BatchNormalization(),\n", "        layers.MaxPooling2D((2, 2)),\n", "        layers.Conv2D(128, (3, 3), activation='relu'),\n", "        layers.MaxPooling2D((2, 2)),\n", "        layers.<PERSON><PERSON>(),\n", "        layers.Dense(128, activation='relu'),\n", "        layers.Dropout(0.3),\n", "        layers.Dense(1, activation='sigmoid')\n", "    ])\n", "\n", "    model.compile(\n", "        optimizer=tf.keras.optimizers.<PERSON>(learning_rate=0.0005),\n", "        loss='binary_crossentropy',\n", "        metrics=['accuracy', tf.keras.metrics.Precision(), tf.keras.metrics.Recall()]\n", "    )\n", "    return model\n"], "metadata": {"id": "N7EtHLiQ11SL"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["##Training Fine Tuned MOdel"], "metadata": {"id": "9iEAorqX15Li"}}, {"cell_type": "code", "source": ["model_ft = build_fine_tuned_model()\n", "history_ft = model_ft.fit(\n", "    train_generator,\n", "    validation_data=val_generator,\n", "    epochs=15,\n", "    callbacks=[\n", "        keras.callbacks.EarlyStopping(patience=3, restore_best_weights=True),\n", "        keras.callbacks.ReduceLROnPlateau(factor=0.2, patience=2)\n", "    ]\n", ")\n"], "metadata": {"id": "Gh4V0Lf_18Hx", "executionInfo": {"status": "ok", "timestamp": 1753938506865, "user_tz": -180, "elapsed": 126119, "user": {"displayName": "<PERSON><PERSON>", "userId": "10262468973935773382"}}, "outputId": "d7084a2a-ce59-44ac-a54e-3f4a5c3c0350", "colab": {"base_uri": "https://localhost:8080/"}}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Epoch 1/15\n", "\u001b[1m3/3\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m28s\u001b[0m 8s/step - accuracy: 0.6914 - loss: 5.8622 - precision_1: 0.8011 - recall_1: 0.7710 - val_accuracy: 0.0417 - val_loss: 1.0280 - val_precision_1: 0.0000e+00 - val_recall_1: 0.0000e+00 - learning_rate: 5.0000e-04\n", "Epoch 2/15\n", "\u001b[1m3/3\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m24s\u001b[0m 8s/step - accuracy: 0.8047 - loss: 3.8926 - precision_1: 0.9852 - recall_1: 0.7669 - val_accuracy: 0.0000e+00 - val_loss: 7.5156 - val_precision_1: 0.0000e+00 - val_recall_1: 0.0000e+00 - learning_rate: 5.0000e-04\n", "Epoch 3/15\n", "\u001b[1m3/3\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m40s\u001b[0m 8s/step - accuracy: 0.9948 - loss: 0.0777 - precision_1: 0.9932 - recall_1: 1.0000 - val_accuracy: 0.0000e+00 - val_loss: 11.2881 - val_precision_1: 0.0000e+00 - val_recall_1: 0.0000e+00 - learning_rate: 5.0000e-04\n", "Epoch 4/15\n", "\u001b[1m3/3\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m34s\u001b[0m 13s/step - accuracy: 0.9909 - loss: 0.0065 - precision_1: 0.9882 - recall_1: 1.0000 - val_accuracy: 0.0000e+00 - val_loss: 11.5310 - val_precision_1: 0.0000e+00 - val_recall_1: 0.0000e+00 - learning_rate: 1.0000e-04\n"]}]}, {"cell_type": "code", "source": ["# Predict again using fine-tuned model\n", "y_pred_probs_ft = model_ft.predict(val_generator)\n", "y_pred_ft = (y_pred_probs_ft > 0.5).astype(\"int32\").flatten()\n", "y_true = val_generator.classes\n", "\n", "# Evaluate fine-tuned model\n", "print(\"🔁 Fine-Tuned Model Evaluation:\")\n", "print(f\"✅ Accuracy: {accuracy_score(y_true, y_pred_ft):.4f}\")\n", "print(f\"🎯 Precision: {precision_score(y_true, y_pred_ft):.4f}\")\n", "print(f\"📥 Recall: {recall_score(y_true, y_pred_ft):.4f}\")\n", "print(f\"📌 F1 Score: {f1_score(y_true, y_pred_ft):.4f}\")\n", "print(f\"📉 RMSE: {np.sqrt(mean_squared_error(y_true, y_pred_ft)):.4f}\")\n"], "metadata": {"id": "Bt4SC8DW2mzw", "executionInfo": {"status": "ok", "timestamp": 1753938508894, "user_tz": -180, "elapsed": 2024, "user": {"displayName": "<PERSON><PERSON>", "userId": "10262468973935773382"}}, "outputId": "d5b900e7-f2e7-47d2-c552-55a11495f165", "colab": {"base_uri": "https://localhost:8080/"}}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\u001b[1m1/1\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m2s\u001b[0m 2s/step\n", "🔁 Fine-Tuned Model Evaluation:\n", "✅ Accuracy: 0.0000\n", "🎯 Precision: 0.0000\n", "📥 Recall: 0.0000\n", "📌 F1 Score: 0.0000\n", "📉 RMSE: 1.0000\n"]}, {"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.11/dist-packages/sklearn/metrics/_classification.py:1565: UndefinedMetricWarning: Recall is ill-defined and being set to 0.0 due to no true samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, f\"{metric.capitalize()} is\", len(result))\n"]}]}, {"cell_type": "code", "source": ["import matplotlib.image as mpimg\n", "\n", "# Set path to your dataset\n", "base_path = \"Dataset\"\n", "\n", "# Get class folders (<PERSON><PERSON>, Diseased)\n", "classes = os.listdir(base_path)\n", "\n", "# Randomly select a class\n", "selected_class = random.choice(classes)\n", "\n", "# Get image files from the selected class\n", "image_files = os.listdir(os.path.join(base_path, selected_class))\n", "\n", "# Randomly select an image file\n", "random_image = random.choice(image_files)\n", "\n", "# Get full path to the image\n", "image_path = os.path.join(base_path, selected_class, random_image)\n", "\n", "# Display the image\n", "img = mpimg.imread(image_path)\n", "plt.imshow(img)\n", "plt.axis('off')\n", "plt.title(f\"Class: {selected_class}\")\n", "plt.show()\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 219}, "id": "Xc0eVQvi2c2L", "executionInfo": {"status": "error", "timestamp": 1753938509254, "user_tz": -180, "elapsed": 349, "user": {"displayName": "<PERSON><PERSON>", "userId": "10262468973935773382"}}, "outputId": "0ba5de50-91fd-42ee-9297-e0fe97bdfd47"}, "execution_count": null, "outputs": [{"output_type": "error", "ename": "FileNotFoundError", "evalue": "[Errno 2] No such file or directory: 'Dataset'", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mFileNotFoundError\u001b[0m                         <PERSON><PERSON> (most recent call last)", "\u001b[0;32m/tmp/ipython-input-**********.py\u001b[0m in \u001b[0;36m<cell line: 0>\u001b[0;34m()\u001b[0m\n\u001b[1;32m      5\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      6\u001b[0m \u001b[0;31m# Get class folders (Healthy, Diseased)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m----> 7\u001b[0;31m \u001b[0mclasses\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mos\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mlistdir\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mbase_path\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m      8\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      9\u001b[0m \u001b[0;31m# Randomly select a class\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mFileNotFoundError\u001b[0m: [Errno 2] No such file or directory: 'Dataset'"]}]}]}