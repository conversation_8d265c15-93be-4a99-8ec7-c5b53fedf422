* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #f5f5f5;
    min-height: 100vh;
    padding: 20px;
    color: #333;
}

.container {
    max-width: 600px;
    margin: 0 auto;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    overflow: hidden;
}

header {
    background: #2c3e50;
    color: white;
    padding: 24px;
    text-align: center;
}

header h1 {
    font-size: 1.8em;
    font-weight: 600;
}

main {
    padding: 24px;
}

.upload-section {
    margin-bottom: 24px;
}

.upload-area {
    border: 2px dashed #ddd;
    border-radius: 8px;
    padding: 40px 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
    background: #fafafa;
}

.upload-area:hover {
    border-color: #2c3e50;
    background-color: #f0f0f0;
}

.upload-area.dragover {
    border-color: #2c3e50;
    background-color: #e8e8e8;
}

.upload-content p {
    font-size: 1.1em;
    margin-bottom: 8px;
    color: #666;
}

.upload-content small {
    color: #999;
    font-size: 0.9em;
}

.results-section {
    margin-bottom: 24px;
}

.image-preview {
    text-align: center;
    margin-bottom: 20px;
}

#imagePreview {
    max-width: 100%;
    max-height: 300px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.result-content {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.result-status {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

#resultText {
    font-size: 1.3em;
    font-weight: 600;
}

#confidenceText {
    font-size: 1.1em;
    font-weight: 500;
    color: #666;
}

.confidence-bar {
    width: 100%;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.confidence-fill {
    height: 100%;
    background: #2c3e50;
    transition: width 0.6s ease;
    border-radius: 4px;
}

.action-buttons {
    display: flex;
    gap: 12px;
    justify-content: center;
}

.analyze-btn {
    background: #2c3e50;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 6px;
    font-size: 1em;
    font-weight: 500;
    cursor: pointer;
    transition: background 0.2s ease;
}

.analyze-btn:hover {
    background: #34495e;
}

.secondary-btn {
    background: #f8f9fa;
    color: #666;
    border: 1px solid #ddd;
    padding: 12px 24px;
    border-radius: 6px;
    font-size: 1em;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.secondary-btn:hover {
    background: #e9ecef;
}

.loading-section {
    text-align: center;
    padding: 40px 20px;
}

.loading-section p {
    color: #666;
    margin-top: 16px;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #2c3e50;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.healthy {
    color: #27ae60;
}

.diseased {
    color: #e74c3c;
}

@media (max-width: 768px) {
    .container {
        margin: 10px;
    }

    header {
        padding: 20px;
    }

    header h1 {
        font-size: 1.5em;
    }

    main {
        padding: 20px;
    }

    .action-buttons {
        flex-direction: column;
    }

    .upload-area {
        padding: 30px 15px;
    }
}