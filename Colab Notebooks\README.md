# 🌱 Plant Disease Detection System

A machine learning-powered web application that detects plant diseases using computer vision and deep learning.

## 📋 Project Overview

This project demonstrates the integration of a trained CNN (Convolutional Neural Network) model with a web frontend for real-time plant disease detection. The system can classify plant images as either "Healthy" or "Diseased" with confidence scores.

## 🏗️ Architecture

```
Frontend (HTML/CSS/JS) ←→ Backend (Flask) ←→ ML Model (TensorFlow/Keras)
```

## 📁 File Structure

```
plant-disease-detector/
├── index.html              # Frontend interface
├── styles.css              # Frontend styling
├── script.js               # Frontend JavaScript
├── backend/
│   ├── app.py              # Flask backend server
│   └── requirements.txt    # Python dependencies
├── plant_disease_model.keras  # Your trained model (from notebook)
└── README.md               # This file
```

## 🚀 Quick Start

### 1. Setup Backend

```bash
# Navigate to backend directory
cd backend

# Install Python dependencies
pip install -r requirements.txt

# Make sure your trained model is in the backend directory
# Copy plant_disease_model.keras from your notebook to backend/
```

### 2. Start Backend Server

```bash
# From the backend directory
python app.py
```

You should see:
```
🚀 Starting Plant Disease Detection API...
✅ Model loaded successfully!
🌐 Server starting on http://localhost:5000
```

### 3. Open Frontend

Simply open `index.html` in your web browser, or use a local server:

```bash
# From the project root directory
python -m http.server 8000
# Then visit http://localhost:8000
```

## 🎯 How to Use

1. **Upload Image**: Click the upload area or drag and drop a plant image
2. **Preview**: See your uploaded image
3. **Analyze**: Click "Analyze Plant" button
4. **Results**: View the classification result and confidence score

## 🔧 Technical Details

### Frontend Features
- **Drag & Drop**: Easy image upload
- **File Validation**: Checks file type and size
- **Real-time Preview**: See uploaded image before analysis
- **Loading Animation**: Visual feedback during processing
- **Responsive Design**: Works on desktop and mobile

### Backend Features
- **Flask API**: RESTful endpoints
- **CORS Support**: Cross-origin requests
- **Image Processing**: Resize and normalize images
- **Model Integration**: Loads and uses your trained CNN
- **Error Handling**: Comprehensive error responses

### API Endpoints

- `GET /` - API information
- `GET /health` - Health check
- `POST /analyze` - Analyze plant image

## 📊 Model Information

The system uses your trained CNN model with:
- **Input Size**: 256x256 pixels
- **Output**: Binary classification (Healthy/Diseased)
- **Confidence**: Probability score for predictions

## 🛠️ Customization

### Adding Features
- **Batch Processing**: Upload multiple images
- **History**: Save previous analyses
- **Export**: Download results as PDF
- **User Accounts**: Personal dashboards

### Styling Changes
- Modify `styles.css` for visual changes
- Add animations or transitions
- Change color schemes

### Backend Enhancements
- Add database for storing results
- Implement user authentication
- Add more detailed analysis
- Support for different model formats

## 🐛 Troubleshooting

### Common Issues

1. **Model not loading**
   - Ensure `plant_disease_model.keras` is in the backend directory
   - Check TensorFlow version compatibility

2. **CORS errors**
   - Backend server must be running on port 5000
   - Check browser console for errors

3. **Image upload issues**
   - Check file size (max 5MB)
   - Ensure image format is supported (JPG, PNG, etc.)

### Debug Mode
- Backend runs in debug mode by default
- Check console for detailed error messages
- Use browser developer tools for frontend debugging

## 📚 Learning Points

This project demonstrates:
- **Full-stack development**: Frontend + Backend + ML
- **API design**: RESTful endpoints
- **File handling**: Image upload and processing
- **Error handling**: User-friendly error messages
- **Responsive design**: Mobile-friendly interface
- **Model deployment**: Serving ML models via web APIs

## 🎓 Class Project Benefits

- **Easy to explain**: Clear separation of concerns
- **Visual demonstration**: Real-time image analysis
- **Complete pipeline**: Data → Model → Web App
- **Professional appearance**: Modern UI/UX
- **Scalable**: Easy to add features

## 📝 License

This project is for educational purposes. Feel free to modify and extend for your needs. 